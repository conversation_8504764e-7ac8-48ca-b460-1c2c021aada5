{"kind": "FETCH", "data": {"headers": {"content-length": "4606", "content-type": "application/json", "date": "Sat, 26 Jul 2025 08:52:59 GMT", "server": "u<PERSON><PERSON>"}, "body": "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", "status": 200, "url": "http://**************:8000/api/blogs/xiang-mu-ce-shi-xiang-qing-ye"}, "revalidate": 10, "tags": ["project-xiang-mu-ce-shi-xiang-qing-ye", "projects"]}