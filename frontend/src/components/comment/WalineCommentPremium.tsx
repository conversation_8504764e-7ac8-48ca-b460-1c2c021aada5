'use client'

import { init } from '@waline/client'
import { useEffect, useRef, useState, useCallback } from 'react'
import { useTheme } from 'next-themes'

interface WalineCommentPremiumProps {
  path: string
  title?: string
  className?: string
}

type SortType = 'latest' | 'oldest' | 'hottest' | 'comment'

export function WalineCommentPremium({ path, title, className = '' }: WalineCommentPremiumProps) {
  const walineRef = useRef<HTMLDivElement>(null)
  const { resolvedTheme } = useTheme()
  const [walineInstance, setWalineInstance] = useState<any>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentSort, setCurrentSort] = useState<SortType>('latest')
  const [commentCount, setCommentCount] = useState(0)
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // 安全销毁实例的函数
  const safeDestroy = useCallback((instance: any) => {
    if (instance && typeof instance.destroy === 'function') {
      try {
        instance.destroy()
      } catch (error) {
        console.warn('Waline destroy error:', error)
      }
    }
  }, [])

  // 增强的DOM操作函数
  const enhanceWalineDOM = useCallback(() => {
    if (!walineRef.current) return

    const container = walineRef.current

    // 添加排序控件
    const addSortControls = () => {
      // 检查是否已经存在排序控件
      if (container.querySelector('.wl-sort-container')) return

      const sortContainer = document.createElement('div')
      sortContainer.className = 'wl-sort-container'
      sortContainer.innerHTML = `
        <button class="wl-sort-btn wl-active" data-sort="latest">Latest</button>
        <button class="wl-sort-btn" data-sort="oldest">Oldest</button>
        <button class="wl-sort-btn" data-sort="hottest">Hottest</button>
        <button class="wl-sort-btn" data-sort="comment">Most Replies</button>
      `

      // 添加事件监听器
      const buttons = sortContainer.querySelectorAll('.wl-sort-btn')
      buttons.forEach(button => {
        button.addEventListener('click', (e) => {
          const target = e.target as HTMLButtonElement
          const sortType = target.getAttribute('data-sort') as SortType
          
          // 更新按钮状态
          buttons.forEach(btn => btn.classList.remove('wl-active'))
          target.classList.add('wl-active')
          
          setCurrentSort(sortType)
          // 这里可以添加实际的排序逻辑
          console.log('Sort by:', sortType)
        })
      })

      // 插入到评论列表前
      const commentsContainer = container.querySelector('.wl-cards')
      if (commentsContainer && commentsContainer.parentNode) {
        commentsContainer.parentNode.insertBefore(sortContainer, commentsContainer)
      }
    }

    // 增强头像显示
    const enhanceAvatars = () => {
      const avatars = container.querySelectorAll('.wl-avatar')
      avatars.forEach(avatar => {
        if (!avatar.querySelector('img')) {
          // 如果没有头像图片，添加默认头像
          const defaultAvatar = document.createElement('div')
          defaultAvatar.style.cssText = `
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, 
              hsl(var(--primary) / 0.2) 0%, 
              hsl(var(--primary) / 0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: hsl(var(--primary));
            border-radius: 50%;
          `
          defaultAvatar.textContent = '👤'
          avatar.appendChild(defaultAvatar)
        }
      })
    }

    // 增强文件上传按钮
    const enhanceFileUpload = () => {
      const actions = container.querySelectorAll('.wl-action')
      actions.forEach(action => {
        const title = action.getAttribute('title')
        if (title && (title.includes('上传') || title.includes('Upload'))) {
          action.innerHTML = '📎'
          action.setAttribute('title', 'Upload File')
        }
        if (title && (title.includes('表情') || title.includes('Emoji'))) {
          action.innerHTML = '😀'
          action.setAttribute('title', 'Insert Emoji')
        }
      })
    }

    // 添加无限滚动支持
    const addInfiniteScroll = () => {
      const loadMoreBtn = container.querySelector('.wl-operation .wl-btn')
      if (loadMoreBtn && !loadMoreBtn.classList.contains('wl-load-more-enhanced')) {
        loadMoreBtn.classList.add('wl-load-more-enhanced', 'wl-load-more')
        
        // 可选：添加自动加载功能
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && entry.target === loadMoreBtn) {
              // 自动点击加载更多按钮
              setTimeout(() => {
                if (loadMoreBtn instanceof HTMLElement) {
                  loadMoreBtn.click()
                }
              }, 500)
            }
          })
        }, { threshold: 0.1 })
        
        observer.observe(loadMoreBtn)
      }
    }

    // 执行所有增强
    setTimeout(() => {
      addSortControls()
      enhanceAvatars()
      enhanceFileUpload()
      addInfiniteScroll()
    }, 300)

    // 监听DOM变化以应用增强
    const observer = new MutationObserver(() => {
      enhanceAvatars()
      enhanceFileUpload()
    })

    observer.observe(container, {
      childList: true,
      subtree: true
    })

    return () => observer.disconnect()
  }, [])

  // 初始化Waline的函数
  const initWaline = useCallback(async () => {
    if (!walineRef.current) return

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 创建新的AbortController
    abortControllerRef.current = new AbortController()

    try {
      setIsLoading(true)
      setError(null)
      
      // 延迟初始化，避免快速切换导致的问题
      await new Promise(resolve => setTimeout(resolve, 100))

      // 检查组件是否仍然存在
      if (!walineRef.current || abortControllerRef.current?.signal.aborted) {
        return
      }

      // 清理容器
      walineRef.current.innerHTML = ''

      // 初始化Waline
      const instance = init({
        el: walineRef.current,
        serverURL: 'https://waline.jyaochen.cn',
        path,
        dark: resolvedTheme === 'dark',
        locale: {
          placeholder: 'Share your thoughts and join the discussion...',
          admin: 'Admin',
          level0: 'Newcomer',
          level1: 'Explorer',
          level2: 'Contributor', 
          level3: 'Expert',
          level4: 'Master',
          level5: 'Legend',
          anonymous: 'Anonymous',
          login: 'Sign In',
          logout: 'Sign Out',
          profile: 'Profile',
          nickError: 'Nickname must be at least 3 characters',
          mailError: 'Please enter a valid email address',
          wordHint: 'Please enter your comment',
          sofa: 'Be the first to share your thoughts!',
          submit: 'Publish Comment',
          reply: 'Reply',
          cancelReply: 'Cancel Reply',
          comment: 'Comment',
          refresh: 'Refresh',
          more: 'Load More Comments...',
          preview: 'Preview',
          emoji: 'Emoji',
          uploadImage: 'Upload Image',
          seconds: 'seconds ago',
          minutes: 'minutes ago',
          hours: 'hours ago',
          days: 'days ago',
          now: 'just now'
        },
        emoji: [
          '//unpkg.com/@waline/emojis@1.2.0/weibo',
          '//unpkg.com/@waline/emojis@1.2.0/alus',
          '//unpkg.com/@waline/emojis@1.2.0/bilibili',
        ],
        meta: ['nick', 'mail', 'link'],
        requiredMeta: ['nick'],
        login: 'enable',
        wordLimit: [0, 1000],
        pageSize: 10,
        lang: 'en-US',
        reaction: true,
        imageUploader: false,
        texRenderer: false,
        search: false
      })

      if (!abortControllerRef.current?.signal.aborted) {
        setWalineInstance(instance)
        setIsInitialized(true)
        setIsLoading(false)
        
        // 添加加载完成的回调和DOM增强
        setTimeout(() => {
          if (walineRef.current) {
            walineRef.current.classList.add('waline-loaded')
            enhanceWalineDOM()
          }
        }, 500)
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Waline initialization error:', error)
        setError('Failed to load comments. Please refresh the page.')
        setIsLoading(false)
      }
    }
  }, [path, resolvedTheme, enhanceWalineDOM])

  // 主useEffect - 处理初始化和清理
  useEffect(() => {
    setIsInitialized(false)
    
    // 使用setTimeout避免在React严格模式下的双重初始化
    initTimeoutRef.current = setTimeout(() => {
      initWaline()
    }, 50)

    return () => {
      // 清理timeout
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current)
      }

      // 取消请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // 安全销毁实例
      if (walineInstance) {
        safeDestroy(walineInstance)
      }

      setIsInitialized(false)
      setWalineInstance(null)
      setIsLoading(true)
      setError(null)
    }
  }, [path, resolvedTheme, initWaline, safeDestroy])

  return (
    <div className={`waline-container-premium ${className}`}>
      {/* 错误状态 */}
      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-sm text-center">
          <p>{error}</p>
          <button 
            onClick={() => {
              setError(null)
              initWaline()
            }}
            className="mt-2 px-3 py-1 bg-destructive/20 hover:bg-destructive/30 rounded-md transition-colors"
          >
            Retry
          </button>
        </div>
      )}
      
      {/* 统计信息 */}
      {isInitialized && !error && (
        <div className="mb-4 text-center">
          <div className="inline-flex items-center gap-4 px-4 py-2 bg-muted/30 rounded-full text-sm text-muted-foreground">
            <span>💬 {commentCount} comments</span>
            <span>•</span>
            <span>Sort: {currentSort}</span>
          </div>
        </div>
      )}
      
      {/* 主要的Waline容器 */}
      <div 
        ref={walineRef} 
        className={`waline-wrapper-premium transition-all duration-500 ${
          isInitialized ? 'opacity-100' : 'opacity-0'
        }`}
        style={{
          // 自定义CSS变量以适配设计系统
          '--waline-theme-color': 'hsl(var(--primary))',
          '--waline-active-color': 'hsl(var(--primary))',
          '--waline-border-color': 'hsl(var(--border))',
          '--waline-bg-color': 'hsl(var(--background))',
          '--waline-bg-color-light': 'hsl(var(--muted))',
          '--waline-text-color': 'hsl(var(--foreground))',
          '--waline-light-grey': 'hsl(var(--muted-foreground))',
          '--waline-white': 'hsl(var(--card))',
          '--waline-color': 'hsl(var(--foreground))',
          '--waline-border-radius': '0.75rem',
          '--waline-avatar-size': '3rem',
          minHeight: isInitialized ? 'auto' : '400px'
        } as React.CSSProperties}
      />
      
      {/* 高级加载状态 */}
      {isLoading && !error && (
        <div className="flex flex-col items-center justify-center py-20 space-y-6">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-muted-foreground/20 border-t-primary rounded-full animate-spin" />
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-primary/50 rounded-full animate-spin" 
                 style={{ animationDirection: 'reverse', animationDuration: '0.8s' }} />
            <div className="absolute inset-2 w-12 h-12 border-2 border-transparent border-b-primary/30 rounded-full animate-spin" 
                 style={{ animationDuration: '1.2s' }} />
          </div>
          <div className="text-center space-y-2">
            <p className="text-lg font-semibold text-foreground">Loading Discussion</p>
            <p className="text-sm text-muted-foreground">Preparing award-winning comment experience...</p>
            <div className="flex items-center justify-center gap-1 mt-3">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default WalineCommentPremium