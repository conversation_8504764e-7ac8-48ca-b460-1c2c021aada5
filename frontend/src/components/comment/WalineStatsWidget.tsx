'use client'

import { useState, useEffect, useCallback } from 'react'
import { Eye, MessageCircle, Heart, TrendingUp, Users, Clock, BarChart3 } from 'lucide-react'
import { PageViewCounter, PageViewCounterPro } from './PageViewCounter'
import { CommentCounter, CommentCounterPro } from './CommentCounter'
import { LikeCounter, LikeCounterPro } from './LikeCounter'

interface WalineStatsWidgetProps {
  path: string
  url?: string
  title?: string
  className?: string
  variant?: 'compact' | 'default' | 'detailed' | 'dashboard'
  showLabels?: boolean
  showIcons?: boolean
  animate?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

interface CombinedStats {
  views: number
  comments: number
  likes: number
  dislikes: number
  engagement: number
  lastUpdated: Date
}

export function WalineStatsWidget({
  path,
  url,
  title,
  className = '',
  variant = 'default',
  showLabels = true,
  showIcons = true,
  animate = true,
  autoRefresh = false,
  refreshInterval = 60000 // 1分钟
}: WalineStatsWidgetProps) {
  const [stats, setStats] = useState<CombinedStats>({
    views: 0,
    comments: 0,
    likes: 0,
    dislikes: 0,
    engagement: 0,
    lastUpdated: new Date()
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 获取综合统计数据
  const fetchCombinedStats = useCallback(async () => {
    try {
      setError(null)
      
      // 并行请求所有数据
      const [viewsResponse, commentsResponse] = await Promise.all([
        fetch(`/api/waline/article/pageview?path=${encodeURIComponent(path)}`),
        fetch(`/api/waline/comment/count?type=count&path=${encodeURIComponent(path)}`)
      ])

      const [viewsData, commentsData] = await Promise.all([
        viewsResponse.json(),
        commentsResponse.json()
      ])

      const views = viewsData.time || 0
      const comments = typeof commentsData.data === 'number' ? commentsData.data : commentsData.data?.count || 0
      const likes = viewsData.reaction?.[0] || 0
      const dislikes = viewsData.reaction?.[1] || 0
      
      // 计算参与度 (评论数 + 点赞数) / 浏览量
      const engagement = views > 0 ? ((comments + likes) / views) * 100 : 0

      setStats({
        views,
        comments,
        likes,
        dislikes,
        engagement,
        lastUpdated: new Date()
      })
    } catch (err) {
      console.error('获取统计数据失败:', err)
      setError(err instanceof Error ? err.message : '获取失败')
    } finally {
      setLoading(false)
    }
  }, [path])

  // 格式化数字显示
  const formatNumber = useCallback((num: number): string => {
    if (num < 1000) return num.toString()
    if (num < 10000) return `${(num / 1000).toFixed(1)}k`
    if (num < 1000000) return `${Math.floor(num / 1000)}k`
    return `${(num / 1000000).toFixed(1)}M`
  }, [])

  // 初始加载
  useEffect(() => {
    fetchCombinedStats()
  }, [fetchCombinedStats])

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      fetchCombinedStats()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchCombinedStats])

  // 渲染不同变体
  const renderContent = () => {
    // 紧凑变体
    if (variant === 'compact') {
      if (loading) {
        return (
          <div className={`flex items-center gap-4 text-xs text-muted-foreground ${className}`}>
            <div className="flex items-center gap-1">
              <Eye className="h-3 w-3" />
              <span className="animate-pulse">-</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="h-3 w-3" />
              <span className="animate-pulse">-</span>
            </div>
            <div className="flex items-center gap-1">
              <Heart className="h-3 w-3" />
              <span className="animate-pulse">-</span>
            </div>
          </div>
        )
      }

      return (
        <div className={`flex items-center gap-4 text-xs text-muted-foreground hover:text-foreground transition-colors ${className}`}>
          <div className="flex items-center gap-1">
            <Eye className="h-3 w-3" />
            <span className="font-medium tabular-nums">{formatNumber(stats.views)}</span>
          </div>
          <div className="flex items-center gap-1">
            <MessageCircle className="h-3 w-3" />
            <span className="font-medium tabular-nums">{formatNumber(stats.comments)}</span>
          </div>
          <div className="flex items-center gap-1">
            <Heart className="h-3 w-3" />
            <span className="font-medium tabular-nums">{formatNumber(stats.likes)}</span>
          </div>
        </div>
      )
    }

    // 默认变体
    if (variant === 'default') {
      if (loading) {
        return (
          <div className={`flex items-center gap-6 ${className}`}>
            <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
              <Eye className="h-4 w-4" />
              <span className="animate-pulse">- views</span>
            </div>
            <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
              <MessageCircle className="h-4 w-4" />
              <span className="animate-pulse">- comments</span>
            </div>
            <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
              <Heart className="h-4 w-4" />
              <span className="animate-pulse">- likes</span>
            </div>
          </div>
        )
      }

      return (
        <div className={`flex items-center gap-6 ${className}`}>
          <div className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors group">
            {showIcons && <Eye className={`h-4 w-4 ${animate ? 'group-hover:scale-110 transition-transform' : ''}`} />}
            <span className="font-medium tabular-nums">{formatNumber(stats.views)}</span>
            {showLabels && <span className="text-xs">{stats.views === 1 ? 'view' : 'views'}</span>}
          </div>
          
          <div className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors group">
            {showIcons && <MessageCircle className={`h-4 w-4 ${animate ? 'group-hover:scale-110 transition-transform' : ''}`} />}
            <span className="font-medium tabular-nums">{formatNumber(stats.comments)}</span>
            {showLabels && <span className="text-xs">{stats.comments === 1 ? 'comment' : 'comments'}</span>}
          </div>
          
          <div className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors group">
            {showIcons && <Heart className={`h-4 w-4 ${animate ? 'group-hover:scale-110 transition-transform' : ''}`} />}
            <span className="font-medium tabular-nums">{formatNumber(stats.likes)}</span>
            {showLabels && <span className="text-xs">{stats.likes === 1 ? 'like' : 'likes'}</span>}
          </div>
        </div>
      )
    }

    // 详细变体
    if (variant === 'detailed') {
      if (loading) {
        return (
          <div className={`space-y-4 ${className}`}>
            <div className="grid grid-cols-3 gap-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="space-y-2">
                  <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                  <div className="h-6 w-20 bg-muted animate-pulse rounded" />
                </div>
              ))}
            </div>
          </div>
        )
      }

      return (
        <div className={`space-y-4 ${className}`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 浏览量卡片 */}
            <div className="p-3 bg-card border rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 text-muted-foreground mb-1">
                <Eye className="h-4 w-4" />
                <span className="text-xs font-medium uppercase tracking-wide">Views</span>
              </div>
              <div className="text-2xl font-bold tabular-nums">{stats.views.toLocaleString()}</div>
            </div>

            {/* 评论卡片 */}
            <div className="p-3 bg-card border rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 text-muted-foreground mb-1">
                <MessageCircle className="h-4 w-4" />
                <span className="text-xs font-medium uppercase tracking-wide">Comments</span>
              </div>
              <div className="text-2xl font-bold tabular-nums">{stats.comments.toLocaleString()}</div>
            </div>

            {/* 点赞卡片 */}
            <div className="p-3 bg-card border rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-center gap-2 text-muted-foreground mb-1">
                <Heart className="h-4 w-4" />
                <span className="text-xs font-medium uppercase tracking-wide">Likes</span>
              </div>
              <div className="text-2xl font-bold tabular-nums">{stats.likes.toLocaleString()}</div>
            </div>
          </div>

          {/* 参与度指标 */}
          {stats.engagement > 0 && (
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2 text-muted-foreground">
                <BarChart3 className="h-4 w-4" />
                <span>Engagement Rate</span>
              </div>
              <span className="font-medium">{stats.engagement.toFixed(1)}%</span>
            </div>
          )}

          {/* 最后更新时间 */}
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>Last updated: {stats.lastUpdated.toLocaleTimeString()}</span>
          </div>
        </div>
      )
    }

    // 仪表板变体
    if (variant === 'dashboard') {
      if (loading) {
        return (
          <div className={`p-6 bg-card border rounded-xl space-y-6 ${className}`}>
            <div className="h-6 w-32 bg-muted animate-pulse rounded" />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="space-y-2">
                  <div className="h-4 w-full bg-muted animate-pulse rounded" />
                  <div className="h-8 w-16 bg-muted animate-pulse rounded" />
                </div>
              ))}
            </div>
          </div>
        )
      }

      return (
        <div className={`p-6 bg-card border rounded-xl space-y-6 ${className}`}>
          {/* 标题 */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Content Analytics</h3>
            <button
              onClick={fetchCombinedStats}
              className="text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              Refresh
            </button>
          </div>

          {/* 主要指标 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Eye className="h-4 w-4" />
                <span className="text-sm">Views</span>
              </div>
              <div className="text-3xl font-bold tabular-nums">{stats.views.toLocaleString()}</div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <MessageCircle className="h-4 w-4" />
                <span className="text-sm">Comments</span>
              </div>
              <div className="text-3xl font-bold tabular-nums">{stats.comments.toLocaleString()}</div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Heart className="h-4 w-4" />
                <span className="text-sm">Likes</span>
              </div>
              <div className="text-3xl font-bold tabular-nums">{stats.likes.toLocaleString()}</div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <TrendingUp className="h-4 w-4" />
                <span className="text-sm">Engagement</span>
              </div>
              <div className="text-3xl font-bold tabular-nums">{stats.engagement.toFixed(1)}%</div>
            </div>
          </div>

          {/* 参与度可视化 */}
          {stats.views > 0 && (
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Engagement Breakdown</div>
              <div className="h-2 bg-muted rounded-full overflow-hidden">
                <div 
                  className="h-full bg-primary rounded-full transition-all duration-500"
                  style={{ width: `${Math.min(stats.engagement, 100)}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>0%</span>
                <span>{stats.engagement.toFixed(1)}%</span>
                <span>100%</span>
              </div>
            </div>
          )}

          {/* 更新时间 */}
          <div className="flex items-center justify-between text-xs text-muted-foreground border-t pt-4">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>Updated: {stats.lastUpdated.toLocaleTimeString()}</span>
            </div>
            {autoRefresh && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span>Auto-refresh enabled</span>
              </div>
            )}
          </div>
        </div>
      )
    }
  }

  if (error) {
    return (
      <div className={`text-sm text-muted-foreground ${className}`}>
        <span className="opacity-50">Stats unavailable</span>
      </div>
    )
  }

  return renderContent()
}

export default WalineStatsWidget