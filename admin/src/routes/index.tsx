// src/routes/index.tsx
import React, { lazy } from 'react';
import { RouteObject, Navigate, Outlet, useLocation } from 'react-router-dom';
import AdminLayout from '../layouts/AdminLayout';
import LoginPage from '../pages/LoginPage';
import Dashboard from '../pages/DashboardPage';
import NotFoundPage from '../pages/NotFoundPage';
const UserListPage = lazy(() => import('../pages/UserListPage'));
const PermissionManagementPage = lazy(() => import('../pages/PermissionManagementPage'));
const SystemMonitorPage = lazy(() => import('../pages/SystemMonitorPage'));
import { useIsAuthenticated } from '../store/authStore';

// Core management pages - 懒加载
const ImageManagerPage = lazy(() => import('../pages/ImageManagerPage'));
const GalleryManagerPage = lazy(() => import('../pages/GalleryManagerPage'));
const UnifiedArticleManagerPage = lazy(() => import('../pages/UnifiedArticleManagerPage'));
const UnifiedArticleEditPage = lazy(() => import('../pages/UnifiedArticleEditPage'));
const UnifiedTagManagerPage = lazy(() => import('../pages/UnifiedTagManagerPage'));

// Personal info management - 懒加载
const ComprehensivePersonalInfoPage = lazy(() => import('../pages/ComprehensivePersonalInfoPage'));
const SocialLinksPage = lazy(() => import('../pages/SocialLinksPage'));
const AboutManagerPage = lazy(() => import('../pages/AboutManagerPage'));
const AboutEditPage = lazy(() => import('../pages/AboutEditPage'));
const EducationManagerPage = lazy(() => import('../pages/EducationManagerPage'));
const CareerManagerPage = lazy(() => import('../pages/CareerManagerPage'));

// Content management - 懒加载
const ContentTemplateManagerPage = lazy(() => import('../pages/ContentTemplateManagerPage'));
const TemplateCategoriesPage = lazy(() => import('../pages/TemplateCategoriesPage'));

// Site settings - 懒加载
const ThemeConfigPage = lazy(() => import('../pages/ThemeConfigPage'));
const TechStackIconsPage = lazy(() => import('../pages/TechStackIconsPage'));
const WebsiteVersionManagerPage = lazy(() => import('../pages/WebsiteVersionManagerPage'));
const WebsiteVersionEditPage = lazy(() => import('../pages/WebsiteVersionEditPage'));
const NavigationManagerPage = lazy(() => import('../pages/NavigationManagerPage'));
const HomepageContentManagerPage = lazy(() => import('../pages/HomepageContentManagerPage'));
const PagesConfigManagerPage = lazy(() => import('../pages/PagesConfigManagerPage'));

// SEO Management pages - 懒加载
const SeoManagerPage = lazy(() => import('../pages/SeoManagerPage'));
const SeoAnalysisPage = lazy(() => import('../pages/SeoAnalysisPage'));
const SeoToolsPage = lazy(() => import('../pages/SeoToolsPage'));
const DomainConfigPage = lazy(() => import('../pages/DomainConfigPage'));
const AdvancedSeoPage = lazy(() => import('../pages/AdvancedSeoPage'));

// Icon Management (core functionality only) - 懒加载
const IconLibraryManagerPage = lazy(() => import('../pages/IconLibraryManagerPage'));
const IconCategoryManagerPage = lazy(() => import('../pages/IconCategoryManagerPage'));
const IconFavoritesPage = lazy(() => import('../pages/IconFavoritesPage'));

// System tools - 懒加载
const ApiHealthCheckPage = lazy(() => import('../pages/ApiHealthCheckPage'));

// Comment management - 懒加载
const CommentManagerPage = lazy(() => import('../pages/CommentManagerPage'));
const WalineConfigPage = lazy(() => import('../pages/WalineConfigPage'));
const WalineDashboardPage = lazy(() => import('../pages/WalineDashboardPage'));

// 受保护路由组件
const ProtectedRoute: React.FC = () => {
  const location = useLocation();
  const isAuthenticated = useIsAuthenticated(); // 从 store 获取认证状态

  if (!isAuthenticated) {
    // 如果未认证，重定向到登录页，并保存原始路径
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 如果已认证，渲染 AdminLayout
  return <AdminLayout />;
};

// 路由配置
export const routes: RouteObject[] = [
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      { index: true, element: <Navigate to="/dashboard" replace /> },
      { path: 'dashboard', element: <Dashboard /> },

      // 内容管理
      { path: 'articles', element: <UnifiedArticleManagerPage /> },
      { path: 'articles/new', element: <UnifiedArticleEditPage /> },
      { path: 'articles/edit/:slug', element: <UnifiedArticleEditPage /> },
      { path: 'articles/tags', element: <UnifiedTagManagerPage /> },
      { path: 'content/templates', element: <ContentTemplateManagerPage /> },
      { path: 'content/template-categories', element: <TemplateCategoriesPage /> },

      // 媒体管理
      { path: 'images', element: <ImageManagerPage /> },
      { path: 'gallery-manager', element: <GalleryManagerPage /> },

      // 用户管理
      { path: 'users', element: <UserListPage /> },
      { path: 'permissions', element: <PermissionManagementPage /> },

      // 站点设置
      { path: 'site-settings/theme-config', element: <ThemeConfigPage /> },
      { path: 'site-settings/tech-stack-icons', element: <TechStackIconsPage /> },
      { path: 'site-settings/homepage-content', element: <HomepageContentManagerPage /> },
      { path: 'site-settings/pages-config', element: <PagesConfigManagerPage /> },
      { path: 'site-settings/navigation', element: <NavigationManagerPage /> },
      { path: 'site-settings/website-versions', element: <WebsiteVersionManagerPage /> },
      { path: 'site-settings/website-versions/new', element: <WebsiteVersionEditPage /> },
      { path: 'site-settings/website-versions/edit/:id', element: <WebsiteVersionEditPage /> },

      // SEO管理
      { path: 'site-settings/seo', element: <SeoManagerPage /> },
      { path: 'site-settings/seo-analysis', element: <SeoAnalysisPage /> },
      { path: 'site-settings/seo-tools', element: <SeoToolsPage /> },
      { path: 'site-settings/domain-config', element: <DomainConfigPage /> },
      { path: 'site-settings/advanced-seo', element: <AdvancedSeoPage /> },

      // 个人信息管理
      { path: 'my/personal-info', element: <ComprehensivePersonalInfoPage /> },
      { path: 'my/social-links', element: <SocialLinksPage /> },
      { path: 'my/about', element: <AboutManagerPage /> },
      { path: 'my/about/new', element: <AboutEditPage /> },
      { path: 'my/about/edit/:id', element: <AboutEditPage /> },
      { path: 'my/education', element: <EducationManagerPage /> },
      { path: 'my/career', element: <CareerManagerPage /> },

      // 图标管理 (核心功能)
      { path: 'icons/libraries', element: <IconLibraryManagerPage /> },
      { path: 'icons/categories', element: <IconCategoryManagerPage /> },
      { path: 'icons/favorites', element: <IconFavoritesPage /> },

      // 评论管理
      { path: 'comments', element: <WalineDashboardPage /> },
      { path: 'comments/manage', element: <CommentManagerPage /> },
      { path: 'comments/config', element: <WalineConfigPage /> },

      // 系统工具 (仅开发环境)
      ...(process.env.NODE_ENV === 'development' ? [
        { path: 'system/api-health', element: <ApiHealthCheckPage /> },
        { path: 'system/monitor', element: <SystemMonitorPage /> },
      ] : []),
    ],
  },
  // 404 路由
  {
    path: '*',
    element: <NotFoundPage />,
  },
];