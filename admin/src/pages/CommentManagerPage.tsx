import React, { useState, useEffect, useCallback } from 'react';
import { 
  MessageOutlined, 
  EyeOutlined, 
  HeartOutlined,
  SearchOutlined, 
  FilterOutlined, 
  ReloadOutlined, 
  SettingOutlined,
  BarChartOutlined,
  SafetyOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  GlobalOutlined,
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  DownloadOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { Card, Row, Col, Statistic, Table, Button, Input, Select, Space, Tag, Tooltip, Modal, message, Spin } from 'antd';
import { walineService, Comment } from '../services/walineService';

const { Option } = Select;
const { confirm } = Modal;

// Comment接口已从walineService导入

interface CommentFilter {
  status: 'all' | 'approved' | 'waiting' | 'spam' | 'deleted';
  search: string;
  dateRange: 'all' | 'today' | 'week' | 'month';
  page: string;
}

interface CommentStats {
  total: number;
  approved: number;
  pending: number;
  spam: number;
  deleted: number;
  hitRate: number;
}

const CommentManagerPage: React.FC = () => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<CommentStats>({
    total: 0,
    approved: 0,
    pending: 0,
    spam: 0,
    deleted: 0,
    hitRate: 0
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [filter, setFilter] = useState<CommentFilter>({
    status: 'all',
    search: '',
    dateRange: 'all',
    page: 'all'
  });

  // 获取评论列表
  const fetchComments = useCallback(async () => {
    try {
      setLoading(true);
      
      const commentsData = await walineService.getRecentComments({
        count: 50
        // token 可以从认证系统获取
      });

      // 客户端过滤
      let filteredData = commentsData;
      
      if (filter.search) {
        const searchTerm = filter.search.toLowerCase();
        filteredData = filteredData.filter((comment: Comment) =>
          comment.comment.toLowerCase().includes(searchTerm) ||
          comment.nick.toLowerCase().includes(searchTerm) ||
          (comment.addr && comment.addr.toLowerCase().includes(searchTerm))
        );
      }

      if (filter.dateRange !== 'all') {
        const now = new Date();
        const filterDate = new Date();
        
        switch (filter.dateRange) {
          case 'today':
            filterDate.setDate(now.getDate() - 1);
            break;
          case 'week':
            filterDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            filterDate.setMonth(now.getMonth() - 1);
            break;
        }

        filteredData = filteredData.filter((comment: Comment) =>
          new Date(comment.time) > filterDate
        );
      }

      setComments(filteredData);
      
      // 计算统计数据
      const total = commentsData.length;
      const approved = commentsData.filter((c: Comment) => c.status === 'approved').length;
      const pending = commentsData.filter((c: Comment) => c.status === 'waiting').length;
      const spam = commentsData.filter((c: Comment) => c.status === 'spam').length;
      const deleted = commentsData.filter((c: Comment) => c.status === 'deleted').length;
      
      setStats({
        total,
        approved,
        pending,
        spam,
        deleted,
        hitRate: 0 // 这里可以从缓存API获取
      });
      
    } catch (err) {
      console.error('获取评论失败:', err);
      message.error('获取评论失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setLoading(false);
    }
  }, [filter]);

  // 批量操作
  const handleBatchAction = useCallback(async (action: 'approve' | 'reject' | 'spam' | 'delete') => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的评论');
      return;
    }

    const actionText = {
      approve: '批准',
      reject: '拒绝',
      spam: '标记为垃圾',
      delete: '删除'
    }[action];

    confirm({
      title: `确认${actionText}`,
      content: `确定要${actionText} ${selectedRowKeys.length} 条评论吗？`,
      onOk: async () => {
        try {
          const objectIds = selectedRowKeys.map(id => Number(id));
          
          if (action === 'delete') {
            await walineService.batchDeleteComments(objectIds, 'admin-token'); // 需要从认证系统获取token
          } else {
            const status = action === 'approve' ? 'approved' : action as 'waiting' | 'spam';
            await walineService.batchUpdateCommentStatus(objectIds, status, 'admin-token');
          }
          
          setSelectedRowKeys([]);
          message.success(`${actionText}成功`);
          fetchComments(); // 重新获取数据
        } catch (err) {
          console.error('批量操作失败:', err);
          message.error('操作失败');
        }
      }
    });
  }, [selectedRowKeys, fetchComments]);

  // 清除缓存 - 简化实现，直接重新获取数据
  const handleClearCache = useCallback(async () => {
    try {
      message.success('缓存已清除');
      fetchComments();
    } catch (err) {
      console.error('清除缓存失败:', err);
      message.error('清除缓存失败');
    }
  }, [fetchComments]);

  // 格式化时间
  const formatDate = useCallback((timestamp: number | string) => {
    const date = typeof timestamp === 'number' ? new Date(timestamp) : new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return date.toLocaleDateString();
  }, []);

  // 获取状态标签
  const getStatusTag = useCallback((status: Comment['status']) => {
    const statusConfig = {
      approved: { color: 'green', text: '已批准', icon: <CheckCircleOutlined style={{ fontSize: 14 }} /> },
      waiting: { color: 'orange', text: '待审核', icon: <ClockCircleOutlined style={{ fontSize: 14 }} /> },
      spam: { color: 'red', text: '垃圾评论', icon: <ExclamationCircleOutlined style={{ fontSize: 14 }} /> },
      deleted: { color: 'default', text: '已删除', icon: <CloseCircleOutlined style={{ fontSize: 14 }} /> }
    };
    
    const config = statusConfig[status] || statusConfig.waiting;
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '用户',
      dataIndex: 'nick',
      key: 'nick',
      width: 120,
      render: (text: string, record: Comment) => (
        <div className="flex items-center gap-2">
          {record.avatar && (
            <img 
              src={record.avatar} 
              alt={record.nick} 
              className="w-8 h-8 rounded-full"
            />
          )}
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-xs text-gray-500">{record.addr || '未知地址'}</div>
          </div>
        </div>
      ),
    },
    {
      title: '评论内容',
      dataIndex: 'comment',
      key: 'comment',
      ellipsis: true,
      render: (text: string, record: Comment) => (
        <div>
          <div className="mb-1">{text}</div>
          <div className="text-xs text-gray-500 flex items-center gap-2">
            <GlobalOutlined style={{ fontSize: 12 }} />
            {record.url}
            {record.like > 0 && (
              <>
                <HeartOutlined style={{ fontSize: 12 }} />
                {record.like}
              </>
            )}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: Comment['status']) => getStatusTag(status),
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 120,
      render: (timestamp: number) => formatDate(timestamp),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: Comment) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                // 编辑功能
                message.info('编辑功能开发中');
              }}
            />
          </Tooltip>
          <Tooltip title="更多">
            <Button
              type="text"
              size="small"
              icon={<MoreOutlined />}
              onClick={() => {
                // 更多操作
                message.info('更多操作开发中');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  // 初始加载
  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <MessageOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          <h1 className="text-2xl font-bold">评论管理</h1>
        </div>
        
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleClearCache}
          >
            清除缓存
          </Button>
          
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={fetchComments}
          >
            刷新数据
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总评论数"
              value={stats.total}
              prefix={<MessageOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已批准"
              value={stats.approved}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待审核"
              value={stats.pending}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="缓存命中率"
              value={stats.hitRate}
              suffix="%"
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 过滤器 */}
      <Card className="mb-6">
        <Space wrap>
          <Input
            placeholder="搜索评论、用户、邮箱..."
            prefix={<SearchOutlined />}
            value={filter.search}
            onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
            style={{ width: 300 }}
          />
          
          <Select
            value={filter.status}
            onChange={(value) => setFilter(prev => ({ ...prev, status: value }))}
            style={{ width: 120 }}
          >
            <Option value="all">全部状态</Option>
            <Option value="approved">已批准</Option>
            <Option value="waiting">待审核</Option>
            <Option value="spam">垃圾评论</Option>
            <Option value="deleted">已删除</Option>
          </Select>

          <Select
            value={filter.dateRange}
            onChange={(value) => setFilter(prev => ({ ...prev, dateRange: value }))}
            style={{ width: 120 }}
          >
            <Option value="all">全部时间</Option>
            <Option value="today">今天</Option>
            <Option value="week">本周</Option>
            <Option value="month">本月</Option>
          </Select>
        </Space>
      </Card>

      {/* 批量操作 */}
      {selectedRowKeys.length > 0 && (
        <Card className="mb-4">
          <div className="flex items-center justify-between">
            <span>
              已选择 <strong>{selectedRowKeys.length}</strong> 条评论
            </span>
            
            <Space>
              <Button
                type="primary"
                icon={<CheckCircleOutlined />}
                onClick={() => handleBatchAction('approve')}
              >
                批准
              </Button>
              
              <Button
                danger
                icon={<ExclamationCircleOutlined />}
                onClick={() => handleBatchAction('spam')}
              >
                标记垃圾
              </Button>
              
              <Button
                icon={<DeleteOutlined />}
                onClick={() => handleBatchAction('delete')}
              >
                删除
              </Button>
            </Space>
          </div>
        </Card>
      )}

      {/* 评论表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={comments}
          rowKey={(record) => record.objectId.toString()}
          rowSelection={rowSelection}
          loading={loading}
          pagination={{
            total: comments.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
};

export default CommentManagerPage;