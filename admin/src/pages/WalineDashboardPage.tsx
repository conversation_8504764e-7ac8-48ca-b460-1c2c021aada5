import React, { useState, useEffect, useCallback } from 'react';
import {
  Card, Row, Col, Statistic, Tabs, Button, Space, message, Spin,
  Table, Tag, Input, Select, Modal, Form, Switch, Alert, Progress,
  List, Avatar, Typography, Divider
} from 'antd';
import {
  MessageOutlined,
  EyeOutlined,
  UserOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  SearchOutlined,
  HeartOutlined,
  GlobalOutlined,
  CalendarOutlined,
  SafetyOutlined,
  ThunderboltOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { walineService, Comment, WalineConfig } from '../services/walineService';
import { useWalineAuth, WalineAuthStatus } from '../hooks/useWalineAuth';
import WalineTestPanel from '../components/WalineTestPanel';

const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;

interface DashboardStats {
  totalComments: number;
  approvedComments: number;
  pendingComments: number;
  spamComments: number;
  totalUsers: number;
  totalPageviews: number;
}

const WalineDashboardPage: React.FC = () => {
  // 认证相关
  const { isAuthenticated, token, login, requireAuth, withAuth } = useWalineAuth();

  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DashboardStats>({
    totalComments: 0,
    approvedComments: 0,
    pendingComments: 0,
    spamComments: 0,
    totalUsers: 0,
    totalPageviews: 0,
  });

  // 健康检查状态
  const [healthStatus, setHealthStatus] = useState<{
    status: 'healthy' | 'unhealthy' | 'checking';
    message: string;
    server_url: string;
    timestamp?: string;
    api_tests?: Record<string, string>;
  }>({
    status: 'checking',
    message: '检查中...',
    server_url: ''
  });
  
  // 评论管理状态
  const [comments, setComments] = useState<Comment[]>([]);
  const [commentLoading, setCommentLoading] = useState(false);
  const [selectedComments, setSelectedComments] = useState<number[]>([]);
  const [commentFilter, setCommentFilter] = useState({
    status: 'all',
    keyword: ''
  });

  // 用户管理状态
  const [users, setUsers] = useState<any[]>([]);
  const [userLoading, setUserLoading] = useState(false);

  // 配置管理状态
  const [config, setConfig] = useState<WalineConfig | null>(null);
  const [configLoading, setConfigLoading] = useState(false);

  // 健康检查
  const checkHealth = useCallback(async () => {
    try {
      setHealthStatus(prev => ({ ...prev, status: 'checking', message: '检查中...' }));
      const health = await walineService.healthCheck();
      setHealthStatus(health);
    } catch (error) {
      console.error('健康检查失败:', error);
      setHealthStatus({
        status: 'unhealthy',
        message: '健康检查失败',
        server_url: '',
        timestamp: new Date().toISOString()
      });
    }
  }, []);

  // 加载统计数据
  const loadStats = useCallback(async () => {
    try {
      setLoading(true);

      // 获取最近评论来计算统计
      const recentComments = await walineService.getRecentComments({
        count: 100,
        token: token || undefined
      });
      const users = await walineService.getUserList({ pageSize: 50 });

      setStats({
        totalComments: recentComments.length,
        approvedComments: recentComments.filter(c => c.status === 'approved').length,
        pendingComments: recentComments.filter(c => c.status === 'waiting').length,
        spamComments: recentComments.filter(c => c.status === 'spam').length,
        totalUsers: users.length,
        totalPageviews: 0, // 可以通过其他API获取
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
      message.error('加载统计数据失败');
    } finally {
      setLoading(false);
    }
  }, [token]);

  // 加载评论列表
  const loadComments = useCallback(async () => {
    try {
      setCommentLoading(true);
      const commentsData = await walineService.getRecentComments({
        count: 100,
        token: token || undefined
      });

      // 根据筛选条件过滤
      let filteredComments = commentsData;
      if (commentFilter.status !== 'all') {
        filteredComments = filteredComments.filter(c => c.status === commentFilter.status);
      }
      if (commentFilter.keyword) {
        const keyword = commentFilter.keyword.toLowerCase();
        filteredComments = filteredComments.filter(c => 
          c.comment.toLowerCase().includes(keyword) || 
          c.nick.toLowerCase().includes(keyword)
        );
      }
      
      setComments(filteredComments);
    } catch (error) {
      console.error('加载评论失败:', error);
      message.error('加载评论失败');
    } finally {
      setCommentLoading(false);
    }
  }, [commentFilter, token]);

  // 加载用户列表
  const loadUsers = useCallback(async () => {
    try {
      setUserLoading(true);
      const usersData = await walineService.getUserList({ pageSize: 100 });
      setUsers(usersData);
    } catch (error) {
      console.error('加载用户失败:', error);
      message.error('加载用户失败');
    } finally {
      setUserLoading(false);
    }
  }, []);

  // 加载配置
  const loadConfig = async () => {
    try {
      setConfigLoading(true);
      const configData = await walineService.getConfig();
      setConfig(configData);
      
      // 检查健康状态
      const health = await walineService.healthCheck();
      setHealthStatus(health);
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
    } finally {
      setConfigLoading(false);
    }
  };

  // 批量操作评论
  const handleBatchAction = useCallback(withAuth(async (action: 'approved' | 'waiting' | 'spam' | 'delete') => {
    if (selectedComments.length === 0) {
      message.warning('请选择要操作的评论');
      return;
    }

    if (!token) {
      message.error('缺少认证token');
      return;
    }

    try {
      if (action === 'delete') {
        await walineService.batchDeleteComments(selectedComments, token);
      } else {
        await walineService.batchUpdateCommentStatus(selectedComments, action, token);
      }

      message.success('操作成功');
      setSelectedComments([]);
      loadComments();
      loadStats();
    } catch (error) {
      console.error('批量操作失败:', error);
      message.error('操作失败');
    }
  }), [selectedComments, token, loadComments, loadStats, withAuth]);

  // 更新配置
  const handleConfigUpdate = async (values: Partial<WalineConfig>) => {
    try {
      setConfigLoading(true);
      await walineService.updateConfig(values);
      message.success('配置更新成功');
      loadConfig();
    } catch (error) {
      console.error('配置更新失败:', error);
      message.error('配置更新失败');
    } finally {
      setConfigLoading(false);
    }
  };

  // 评论表格列定义
  const commentColumns = [
    {
      title: '用户',
      dataIndex: 'nick',
      key: 'nick',
      render: (text: string, record: Comment) => (
        <div className="flex items-center gap-2">
          {record.avatar && <img src={record.avatar} alt={text} className="w-8 h-8 rounded-full" />}
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-xs text-gray-500">{record.addr || '未知'}</div>
          </div>
        </div>
      ),
    },
    {
      title: '评论内容',
      dataIndex: 'comment',
      key: 'comment',
      ellipsis: true,
      render: (text: string) => <div dangerouslySetInnerHTML={{ __html: text }} />,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          approved: { color: 'green', text: '已批准', icon: <CheckCircleOutlined /> },
          waiting: { color: 'orange', text: '待审核', icon: <ClockCircleOutlined /> },
          spam: { color: 'red', text: '垃圾评论', icon: <ExclamationCircleOutlined /> },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.waiting;
        return <Tag color={config.color} icon={config.icon}>{config.text}</Tag>;
      },
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      render: (time: number) => new Date(time).toLocaleString(),
    },
    {
      title: '页面',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
    },
  ];

  // 用户表格列定义
  const userColumns = [
    {
      title: '用户',
      dataIndex: 'nick',
      key: 'nick',
      render: (text: string, record: any) => (
        <div className="flex items-center gap-2">
          {record.avatar && <img src={record.avatar} alt={text} className="w-8 h-8 rounded-full" />}
          <div>
            <div className="font-medium">{text}</div>
            {record.label && <Tag size="small">{record.label}</Tag>}
          </div>
        </div>
      ),
    },
    {
      title: '链接',
      dataIndex: 'link',
      key: 'link',
      render: (link: string) => link ? <a href={link} target="_blank" rel="noopener noreferrer">{link}</a> : '-',
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      render: (level: number) => level || 0,
    },
    {
      title: '评论数',
      dataIndex: 'count',
      key: 'count',
    },
  ];

  // 初始化
  useEffect(() => {
    checkHealth();
    loadStats();
    loadConfig();
  }, [checkHealth, loadStats]);

  useEffect(() => {
    if (activeTab === 'comments') {
      loadComments();
    } else if (activeTab === 'users') {
      loadUsers();
    }
  }, [activeTab, commentFilter, loadComments, loadUsers]);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Waline 管理中心</h1>
          <p className="text-gray-600 mt-1">评论系统统一管理平台</p>
        </div>
        
        <Space>
          <WalineAuthStatus showUserInfo={true} showActions={true} />
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              checkHealth();
              loadStats();
              loadComments();
              loadUsers();
              loadConfig();
            }}
            loading={loading}
          >
            全部刷新
          </Button>
        </Space>
      </div>

      {/* 认证状态提示 */}
      {!isAuthenticated && (
        <Alert
          message="需要Waline管理员认证"
          description={
            <div>
              <p>部分管理功能需要Waline管理员权限，请先登录。</p>
              <Button type="primary" size="small" onClick={() => login()}>
                立即登录
              </Button>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 健康检查状态 */}
      <Card className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${
              healthStatus.status === 'healthy' ? 'bg-green-500' :
              healthStatus.status === 'unhealthy' ? 'bg-red-500' : 'bg-yellow-500'
            }`} />
            <div>
              <div className="font-medium">
                Waline 服务状态: {
                  healthStatus.status === 'healthy' ? '正常' :
                  healthStatus.status === 'unhealthy' ? '异常' : '检查中'
                }
              </div>
              <div className="text-sm text-gray-500">
                服务器: {healthStatus.server_url || '未知'}
                {healthStatus.timestamp && (
                  <span className="ml-2">
                    更新时间: {new Date(healthStatus.timestamp).toLocaleString()}
                  </span>
                )}
              </div>
            </div>
          </div>
          <Button
            size="small"
            icon={<SafetyOutlined />}
            onClick={checkHealth}
            loading={healthStatus.status === 'checking'}
          >
            重新检查
          </Button>
        </div>
        {healthStatus.api_tests && (
          <div className="mt-3 pt-3 border-t">
            <div className="text-sm text-gray-600 mb-2">API 测试结果:</div>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(healthStatus.api_tests).map(([api, status]) => (
                <div key={api} className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    status === '正常' ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <span className="text-sm">{api}: {status}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总评论数"
              value={stats.totalComments}
              prefix={<MessageOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已批准"
              value={stats.approvedComments}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待审核"
              value={stats.pendingComments}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* 功能标签页 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="overview">
            <div className="space-y-4">
              <Row gutter={16}>
                <Col span={12}>
                  <Card title="服务状态" size="small">
                    {healthStatus ? (
                      <div className={`p-4 rounded ${healthStatus.status === 'healthy' ? 'bg-green-50' : 'bg-red-50'}`}>
                        <div className="flex items-center gap-2">
                          {healthStatus.status === 'healthy' ? 
                            <CheckCircleOutlined className="text-green-500" /> : 
                            <ExclamationCircleOutlined className="text-red-500" />
                          }
                          <span className="font-medium">
                            {healthStatus.status === 'healthy' ? '服务正常' : '服务异常'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">{healthStatus.message}</p>
                        {config && (
                          <p className="text-xs text-gray-500 mt-1">
                            服务器: {config.server_url}
                          </p>
                        )}
                      </div>
                    ) : (
                      <Spin />
                    )}
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="快速操作" size="small">
                    <Space wrap>
                      <Button type="primary" onClick={() => setActiveTab('comments')}>
                        管理评论
                      </Button>
                      <Button onClick={() => setActiveTab('users')}>
                        管理用户
                      </Button>
                      <Button onClick={() => setActiveTab('settings')}>
                        系统设置
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </div>
          </TabPane>

          <TabPane tab={`评论管理 (${stats.totalComments})`} key="comments">
            <div className="space-y-4">
              {/* 筛选和操作栏 */}
              <div className="flex justify-between items-center">
                <Space>
                  <Search
                    placeholder="搜索评论内容或用户"
                    style={{ width: 300 }}
                    value={commentFilter.keyword}
                    onChange={(e) => setCommentFilter(prev => ({ ...prev, keyword: e.target.value }))}
                  />
                  <Select
                    value={commentFilter.status}
                    onChange={(value) => setCommentFilter(prev => ({ ...prev, status: value }))}
                    style={{ width: 120 }}
                  >
                    <Option value="all">全部状态</Option>
                    <Option value="approved">已批准</Option>
                    <Option value="waiting">待审核</Option>
                    <Option value="spam">垃圾评论</Option>
                  </Select>
                </Space>

                {selectedComments.length > 0 && (
                  <Space>
                    <span>已选择 {selectedComments.length} 条</span>
                    <Button 
                      type="primary" 
                      size="small"
                      onClick={() => handleBatchAction('approved')}
                    >
                      批准
                    </Button>
                    <Button 
                      danger 
                      size="small"
                      onClick={() => handleBatchAction('spam')}
                    >
                      标记垃圾
                    </Button>
                    <Button 
                      size="small"
                      onClick={() => handleBatchAction('delete')}
                    >
                      删除
                    </Button>
                  </Space>
                )}
              </div>

              <Table
                columns={commentColumns}
                dataSource={comments}
                loading={commentLoading}
                rowKey={(record) => record.objectId.toString()}
                rowSelection={{
                  selectedRowKeys: selectedComments.map(id => id.toString()),
                  onChange: (keys) => setSelectedComments(keys.map(k => Number(k))),
                }}
                pagination={{
                  pageSize: 20,
                  showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
                }}
              />
            </div>
          </TabPane>

          <TabPane tab={`用户管理 (${stats.totalUsers})`} key="users">
            <Table
              columns={userColumns}
              dataSource={users}
              loading={userLoading}
              rowKey="nick"
              pagination={{
                pageSize: 20,
                showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
              }}
            />
          </TabPane>

          <TabPane tab="系统设置" key="settings">
            <div className="max-w-2xl">
              <Form
                layout="vertical"
                initialValues={config}
                onFinish={handleConfigUpdate}
              >
                <Form.Item
                  label="Waline 服务器地址"
                  name="server_url"
                  rules={[{ required: true, message: '请输入服务器地址' }]}
                >
                  <Input placeholder="https://waline.jyaochen.cn" />
                </Form.Item>

                <Form.Item
                  label="语言设置"
                  name="lang"
                  rules={[{ required: true, message: '请选择语言' }]}
                >
                  <Select>
                    <Option value="zh-CN">简体中文</Option>
                    <Option value="zh-TW">繁体中文</Option>
                    <Option value="en-US">English</Option>
                    <Option value="ja-JP">日本語</Option>
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" loading={configLoading}>
                      保存配置
                    </Button>
                    <Button onClick={loadConfig}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </div>
          </TabPane>

          <TabPane tab="功能测试" key="test">
            <WalineTestPanel />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default WalineDashboardPage;