import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Space, message, Spin, Alert } from 'antd';
import { SaveOutlined, ReloadOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { walineService, WalineConfig } from '../services/walineService';

const WalineConfigPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [config, setConfig] = useState<WalineConfig | null>(null);
  const [healthStatus, setHealthStatus] = useState<{
    status: 'healthy' | 'unhealthy';
    message: string;
  } | null>(null);

  // 获取当前配置
  const fetchConfig = async () => {
    try {
      setLoading(true);
      const currentConfig = await walineService.getConfig();
      setConfig(currentConfig);
      form.setFieldsValue(currentConfig);
    } catch (error) {
      console.error('获取配置失败:', error);
      message.error('获取配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const handleSave = async (values: WalineConfig) => {
    try {
      setLoading(true);
      const updatedConfig = await walineService.updateConfig(values);
      setConfig(updatedConfig);
      message.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      setTesting(true);
      const health = await walineService.healthCheck();
      setHealthStatus(health);
      
      if (health.status === 'healthy') {
        message.success('连接测试成功');
      } else {
        message.error('连接测试失败');
      }
    } catch (error) {
      console.error('连接测试失败:', error);
      setHealthStatus({
        status: 'unhealthy',
        message: '连接测试失败'
      });
      message.error('连接测试失败');
    } finally {
      setTesting(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchConfig();
  }, []);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Waline 配置</h1>
          <p className="text-gray-600 mt-1">管理 Waline 评论系统的服务器配置</p>
        </div>
        
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchConfig}
            loading={loading}
          >
            重新加载
          </Button>
          <Button 
            type="primary" 
            icon={<CheckCircleOutlined />}
            onClick={handleTestConnection}
            loading={testing}
          >
            测试连接
          </Button>
        </Space>
      </div>

      {/* 连接状态显示 */}
      {healthStatus && (
        <Alert
          type={healthStatus.status === 'healthy' ? 'success' : 'error'}
          message={healthStatus.status === 'healthy' ? '服务连接正常' : '服务连接异常'}
          description={healthStatus.message}
          showIcon
          className="mb-6"
          closable
          onClose={() => setHealthStatus(null)}
        />
      )}

      <Card title="基础配置" loading={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={config}
        >
          <Form.Item
            label="Waline 服务器地址"
            name="server_url"
            rules={[
              { required: true, message: '请输入 Waline 服务器地址' },
              { type: 'url', message: '请输入有效的 URL 地址' }
            ]}
            extra="Waline 后端服务的完整 URL 地址，例如：https://waline.example.com"
          >
            <Input 
              placeholder="https://waline.jyaochen.cn"
              disabled={loading}
            />
          </Form.Item>

          <Form.Item
            label="语言设置"
            name="lang"
            rules={[
              { required: true, message: '请输入语言代码' },
            ]}
            extra="界面显示语言，支持 zh-CN, en-US 等"
          >
            <Input 
              placeholder="zh-CN"
              disabled={loading}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存配置
              </Button>
              <Button 
                onClick={() => form.resetFields()}
                disabled={loading}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 配置说明 */}
      <Card title="配置说明" className="mt-6">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">服务器地址配置</h4>
            <p className="text-gray-600 text-sm">
              请确保 Waline 服务器地址正确且可访问。地址应包含完整的协议（http:// 或 https://）和域名。
              更改此配置后，所有评论数据将从新的服务器获取。
            </p>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">语言设置</h4>
            <p className="text-gray-600 text-sm">
              设置 Waline 客户端的显示语言。支持的语言代码包括：
            </p>
            <ul className="text-gray-600 text-sm mt-1 ml-4">
              <li>• zh-CN: 简体中文</li>
              <li>• zh-TW: 繁体中文</li>
              <li>• en-US: 英语（美国）</li>
              <li>• ja-JP: 日语</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium mb-2">测试连接</h4>
            <p className="text-gray-600 text-sm">
              使用"测试连接"功能可以验证当前配置的 Waline 服务器是否可正常访问。
              建议在更改配置后进行连接测试。
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default WalineConfigPage;