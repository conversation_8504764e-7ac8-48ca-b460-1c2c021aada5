// Waline API 服务层
export interface WalineConfig {
  server_url: string;
  lang: string;
}

export interface Comment {
  objectId: number;
  time: number;
  comment: string;
  orig: string;
  like: number;
  nick: string;
  link: string;
  avatar: string;
  type?: 'administrator' | 'guest';
  user_id?: number;
  addr?: string;
  browser?: string;
  os?: string;
  level?: number;
  label?: string;
  status?: 'approved' | 'waiting' | 'spam';
  url: string; // 页面路径
}

export interface GetCommentsResponse {
  count: number;
  page: number;
  pageSize: number;
  data: Comment[];
  totalPages: number;
}

export interface RecentCommentsResponse {
  data: Comment[];
}

class WalineService {
  private baseURL = '/api/waline';
  private config: WalineConfig | null = null;

  // 获取Waline配置
  async getConfig(): Promise<WalineConfig> {
    if (this.config) {
      return this.config;
    }

    const response = await fetch('/api/system-config/waline/config');
    if (!response.ok) {
      throw new Error('获取Waline配置失败');
    }
    
    this.config = await response.json();
    return this.config;
  }

  // 更新Waline配置
  async updateConfig(config: Partial<WalineConfig>): Promise<WalineConfig> {
    const response = await fetch('/api/system-config/waline/config', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(config),
    });

    if (!response.ok) {
      throw new Error('更新Waline配置失败');
    }

    const updatedConfig = await response.json();
    this.config = updatedConfig;
    return updatedConfig;
  }

  // 获取评论列表
  async getComments(params: {
    path?: string;
    page?: number;
    pageSize?: number;
    sortBy?: string;
    token?: string;
  } = {}): Promise<GetCommentsResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.path) searchParams.append('path', params.path);
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString());
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.token) searchParams.append('token', params.token);

    const response = await fetch(`${this.baseURL}/comment?${searchParams}`);
    
    if (!response.ok) {
      throw new Error(`获取评论失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取最近评论
  async getRecentComments(params: {
    count?: number;
    token?: string;
  } = {}): Promise<Comment[]> {
    const searchParams = new URLSearchParams();
    
    if (params.count) searchParams.append('count', params.count.toString());
    if (params.token) searchParams.append('token', params.token);

    const response = await fetch(`${this.baseURL}/comment/recent?${searchParams}`);
    
    if (!response.ok) {
      throw new Error(`获取最近评论失败: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || [];
  }

  // 管理员获取评论
  async getAdminComments(params: {
    page?: number;
    pageSize?: number;
    type?: string;
    keyword?: string;
    token: string;
  }): Promise<GetCommentsResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString());
    if (params.type) searchParams.append('type', params.type);
    if (params.keyword) searchParams.append('keyword', params.keyword);
    searchParams.append('token', params.token);

    const response = await fetch(`${this.baseURL}/admin/comment?${searchParams}`);
    
    if (!response.ok) {
      throw new Error(`获取管理员评论失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 更新评论状态
  async updateCommentStatus(
    objectId: number,
    status: 'approved' | 'waiting' | 'spam',
    token: string
  ): Promise<Comment> {
    const response = await fetch(
      `${this.baseURL}/admin/comment/${objectId}/status?token=${token}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      }
    );

    if (!response.ok) {
      throw new Error(`更新评论状态失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 删除评论
  async deleteComment(objectId: number, token: string): Promise<void> {
    const response = await fetch(
      `${this.baseURL}/comment/${objectId}?token=${token}`,
      {
        method: 'DELETE',
      }
    );

    if (!response.ok) {
      throw new Error(`删除评论失败: ${response.statusText}`);
    }
  }

  // 批量更新评论状态
  async batchUpdateCommentStatus(
    objectIds: number[],
    status: 'approved' | 'waiting' | 'spam',
    token: string
  ): Promise<void> {
    const promises = objectIds.map(objectId =>
      this.updateCommentStatus(objectId, status, token)
    );

    await Promise.all(promises);
  }

  // 批量删除评论
  async batchDeleteComments(objectIds: number[], token: string): Promise<void> {
    const promises = objectIds.map(objectId =>
      this.deleteComment(objectId, token)
    );

    await Promise.all(promises);
  }

  // 获取评论计数
  async getCommentCount(paths: string[]): Promise<number[]> {
    const searchParams = new URLSearchParams();
    paths.forEach(path => searchParams.append('paths', path));

    const response = await fetch(`${this.baseURL}/comment/count?${searchParams}`);
    
    if (!response.ok) {
      throw new Error(`获取评论计数失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取页面浏览量
  async getPageview(paths: string[]): Promise<any[]> {
    const searchParams = new URLSearchParams();
    paths.forEach(path => searchParams.append('paths', path));

    const response = await fetch(`${this.baseURL}/pageview?${searchParams}`);
    
    if (!response.ok) {
      throw new Error(`获取页面浏览量失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 更新页面浏览量
  async updatePageview(path: string): Promise<any> {
    const response = await fetch(`${this.baseURL}/pageview`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ path }),
    });

    if (!response.ok) {
      throw new Error(`更新页面浏览量失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取用户列表
  async getUserList(pageSize: number = 10): Promise<any[]> {
    const response = await fetch(`${this.baseURL}/user?pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error(`获取用户列表失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 健康检查
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    server_url: string;
    lang: string;
    message: string;
  }> {
    const response = await fetch(`${this.baseURL}/health`);
    
    if (!response.ok) {
      throw new Error(`健康检查失败: ${response.statusText}`);
    }

    return response.json();
  }
}

export const walineService = new WalineService();