from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from database.database import get_db
from typing import Optional, List, Dict, Any
import httpx
from datetime import datetime, timedelta
from pydantic import BaseModel
from routers.system_config import get_config_value
from utils.auth import get_current_user
import logging
import time
from functools import wraps

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/waline", tags=["waline"])

DEFAULT_TIMEOUT = 30
DEFAULT_LANG = "zh-CN"

# === 基础API包装器 ===

async def get_waline_config(db: AsyncSession) -> tuple[str, str]:
    """获取Waline配置"""
    server_url = await get_config_value(db, "waline.server_url", "https://waline.jyaochen.cn")
    lang = await get_config_value(db, "waline.lang", DEFAULT_LANG)
    return server_url, lang

async def make_waline_request(
    db: AsyncSession,
    endpoint: str, 
    params: Optional[Dict] = None, 
    method: str = "GET",
    data: Optional[Dict] = None,
    token: Optional[str] = None
) -> Dict[str, Any]:
    """
    向Waline服务器发送请求的通用函数
    """
    server_url, lang = await get_waline_config(db)
    url = f"{server_url}/api/{endpoint.lstrip('/')}"
    
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
        try:
            if method.upper() == "GET":
                response = await client.get(url, params=params or {}, headers=headers)
            elif method.upper() == "POST":
                response = await client.post(url, params=params or {}, json=data or {}, headers=headers)
            elif method.upper() == "PUT":
                response = await client.put(url, params=params or {}, json=data or {}, headers=headers)
            elif method.upper() == "DELETE":
                response = await client.delete(url, params=params or {}, headers=headers)
            else:
                raise ValueError(f"不支持的请求方法: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except httpx.TimeoutException:
            logger.error(f"Waline API请求超时: {url}")
            raise HTTPException(status_code=504, detail="Waline服务响应超时")
        except httpx.HTTPStatusError as e:
            logger.error(f"Waline API请求失败: {e.response.status_code} - {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code, 
                detail=f"Waline API错误: {e.response.text}"
            )
        except Exception as e:
            logger.error(f"Waline API请求异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

# === 评论相关API ===

class CommentQueryParams(BaseModel):
    path: str
    page: int = 1
    pageSize: int = 10
    sortBy: str = "createdAt"
    token: Optional[str] = None

@router.get("/comment", summary="获取评论列表")
async def get_comments(
    path: str = Query(..., description="页面路径"),
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(10, ge=1, le=100, description="每页数量"),
    sortBy: str = Query("createdAt", description="排序字段"),
    token: Optional[str] = Query(None, description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """获取指定页面的评论列表"""
    params = {
        "path": path,
        "page": page,
        "pageSize": pageSize,
        "sortBy": sortBy
    }
    
    result = await make_waline_request(db, "comment", params=params, token=token)
    return result

@router.post("/comment", summary="添加评论")
async def add_comment(
    comment_data: dict,
    token: Optional[str] = Query(None, description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """添加新评论"""
    result = await make_waline_request(db, "comment", method="POST", data=comment_data, token=token)
    return result

@router.put("/comment/{object_id}", summary="更新评论")
async def update_comment(
    object_id: int,
    comment_data: dict,
    token: str = Query(..., description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """更新评论"""
    result = await make_waline_request(db, f"comment/{object_id}", method="PUT", data=comment_data, token=token)
    return result

@router.delete("/comment/{object_id}", summary="删除评论")
async def delete_comment(
    object_id: int,
    token: str = Query(..., description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """删除评论"""
    result = await make_waline_request(db, f"comment/{object_id}", method="DELETE", token=token)
    return result

@router.get("/comment/recent", summary="获取最近评论")
async def get_recent_comments(
    count: int = Query(10, ge=1, le=50, description="获取数量"),
    token: Optional[str] = Query(None, description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """获取最近评论"""
    # 直接调用Waline的recent评论API
    server_url, lang = await get_waline_config(db)
    
    async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
        try:
            # 使用Waline的标准recent评论API
            url = f"{server_url}/api/comment"
            params = {"type": "recent", "count": count}
            
            headers = {}
            if token:
                headers["Authorization"] = f"Bearer {token}"
            
            response = await client.get(url, params=params, headers=headers)
            response.raise_for_status()
            
            return response.json()
            
        except httpx.TimeoutException:
            logger.error(f"Waline API请求超时: {url}")
            raise HTTPException(status_code=504, detail="Waline服务响应超时")
        except httpx.HTTPStatusError as e:
            logger.error(f"Waline API请求失败: {e.response.status_code} - {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code, 
                detail=f"Waline API错误: {e.response.text}"
            )
        except Exception as e:
            logger.error(f"Waline API请求异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

@router.get("/comment/count", summary="获取评论计数")
async def get_comment_count(
    paths: List[str] = Query(..., description="页面路径列表"),
    db: AsyncSession = Depends(get_db)
):
    """获取多个页面的评论计数"""
    params = {"paths": paths}
    
    result = await make_waline_request(db, "comment", params=params)
    return result

# === 页面浏览量API ===

@router.get("/pageview", summary="获取页面浏览量")
async def get_pageview(
    paths: List[str] = Query(..., description="页面路径列表"),
    db: AsyncSession = Depends(get_db)
):
    """获取页面浏览量"""
    params = {"paths": paths}
    
    result = await make_waline_request(db, "article", params=params)
    return result

@router.post("/pageview", summary="更新页面浏览量")
async def update_pageview(
    path: str,
    db: AsyncSession = Depends(get_db)
):
    """更新页面浏览量"""
    data = {"path": path}
    
    result = await make_waline_request(db, "article", method="POST", data=data)
    return result

# === 文章计数器API ===

@router.get("/counter", summary="获取文章计数器")
async def get_article_counter(
    paths: List[str] = Query(..., description="页面路径列表"),
    type: List[str] = Query(..., description="计数器类型列表"),
    db: AsyncSession = Depends(get_db)
):
    """获取文章计数器"""
    params = {
        "paths": paths,
        "type": type
    }
    
    result = await make_waline_request(db, "article", params=params)
    return result

@router.post("/counter", summary="更新文章计数器")
async def update_article_counter(
    path: str,
    type: str,
    action: str = "inc",
    db: AsyncSession = Depends(get_db)
):
    """更新文章计数器"""
    data = {
        "path": path,
        "type": type,
        "action": action
    }
    
    result = await make_waline_request(db, "article", method="POST", data=data)
    return result

# === 用户管理API ===

@router.get("/user", summary="获取用户列表")
async def get_user_list(
    pageSize: int = Query(10, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表"""
    params = {"pageSize": pageSize}
    
    result = await make_waline_request(db, "user", params=params)
    return result

@router.post("/login", summary="用户登录")
async def login(
    db: AsyncSession = Depends(get_db)
):
    """用户登录"""
    result = await make_waline_request(db, "token", method="POST")
    return result

# === 管理员API ===

@router.get("/admin/comment", summary="管理员获取评论")
async def admin_get_comments(
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(10, ge=1, le=100, description="每页数量"),
    type: Optional[str] = Query(None, description="评论类型筛选"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    token: str = Query(..., description="管理员令牌"),
    db: AsyncSession = Depends(get_db)
):
    """管理员获取评论列表"""
    params = {
        "page": page,
        "pageSize": pageSize
    }
    
    if type:
        params["type"] = type
    if keyword:
        params["keyword"] = keyword
    
    result = await make_waline_request(db, "comment", params=params, token=token)
    return result

@router.put("/admin/comment/{object_id}/status", summary="管理员更新评论状态")
async def admin_update_comment_status(
    object_id: int,
    status: str,
    token: str = Query(..., description="管理员令牌"),
    db: AsyncSession = Depends(get_db)
):
    """管理员更新评论状态"""
    data = {"status": status}
    
    result = await make_waline_request(db, f"comment/{object_id}", method="PUT", data=data, token=token)
    return result

# === 健康检查 ===

@router.get("/health", summary="Waline服务健康检查")
async def waline_health_check(db: AsyncSession = Depends(get_db)):
    """检查Waline服务状态"""
    try:
        server_url, lang = await get_waline_config(db)
        
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get(f"{server_url}/api")
            response.raise_for_status()
            
        return {
            "status": "healthy",
            "server_url": server_url,
            "lang": lang,
            "message": "Waline服务正常"
        }
    except Exception as e:
        logger.error(f"Waline健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "server_url": server_url if 'server_url' in locals() else "unknown",
            "message": f"Waline服务异常: {str(e)}"
        }