from fastapi import APIRouter, HTTPException, Query, Depends, Body
from sqlalchemy.ext.asyncio import AsyncSession
from database.database import get_db
from typing import Optional, List, Dict, Any, Union
import httpx
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from routers.system_config import get_config_value
from utils.auth import get_current_user
import logging
import time
import json
from functools import wraps

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/waline", tags=["waline"])

DEFAULT_TIMEOUT = 30
DEFAULT_LANG = "zh-CN"

# === Pydantic模型定义 ===

class WalineCommentData(BaseModel):
    nick: str = Field(..., description="用户昵称")
    mail: Optional[str] = Field(None, description="用户邮箱")
    link: Optional[str] = Field(None, description="用户网站")
    comment: str = Field(..., description="评论内容")
    ua: str = Field(..., description="用户代理")
    url: str = Field(..., description="页面路径")
    pid: Optional[int] = Field(None, description="父评论ID")
    rid: Optional[int] = Field(None, description="根评论ID")
    at: Optional[str] = Field(None, description="@用户")
    recaptchaV3: Optional[str] = Field(None, description="reCAPTCHA令牌")
    turnstile: Optional[str] = Field(None, description="Turnstile令牌")

class UpdateCommentData(BaseModel):
    like: Optional[bool] = Field(None, description="点赞/取消点赞")
    status: Optional[str] = Field(None, description="评论状态")
    sticky: Optional[int] = Field(None, description="置顶状态")
    comment: Optional[str] = Field(None, description="评论内容")
    nick: Optional[str] = Field(None, description="用户昵称")
    mail: Optional[str] = Field(None, description="用户邮箱")
    link: Optional[str] = Field(None, description="用户网站")

# === 基础API包装器 ===

async def get_waline_config(db: AsyncSession) -> tuple[str, str]:
    """获取Waline配置"""
    server_url = await get_config_value(db, "waline.server_url", "https://waline.jyaochen.cn")
    lang = await get_config_value(db, "waline.lang", DEFAULT_LANG)
    return server_url, lang

async def make_waline_request(
    db: AsyncSession,
    endpoint: str,
    params: Optional[Dict] = None,
    method: str = "GET",
    data: Optional[Dict] = None,
    token: Optional[str] = None,
    timeout: int = DEFAULT_TIMEOUT
) -> Dict[str, Any]:
    """
    向Waline服务器发送请求的通用函数
    符合官方API规范
    """
    server_url, lang = await get_waline_config(db)
    url = f"{server_url}/api/{endpoint.lstrip('/')}"

    # 构建请求头
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Waline-Admin-Backend/1.0"
    }

    if token:
        headers["Authorization"] = f"Bearer {token}"

    # 添加语言参数到请求参数中
    if params is None:
        params = {}
    if "lang" not in params:
        params["lang"] = lang

    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            logger.info(f"Waline API请求: {method} {url} - 参数: {params}")

            if method.upper() == "GET":
                response = await client.get(url, params=params, headers=headers)
            elif method.upper() == "POST":
                response = await client.post(url, params=params, json=data, headers=headers)
            elif method.upper() == "PUT":
                response = await client.put(url, params=params, json=data, headers=headers)
            elif method.upper() == "DELETE":
                response = await client.delete(url, params=params, headers=headers)
            else:
                raise ValueError(f"不支持的请求方法: {method}")

            # 记录响应状态
            logger.info(f"Waline API响应: {response.status_code}")

            # 处理不同的响应状态
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 201:
                return response.json()
            elif response.status_code == 204:
                return {"errno": 0, "errmsg": "success", "data": ""}
            else:
                response.raise_for_status()

        except httpx.TimeoutException:
            logger.error(f"Waline API请求超时: {url}")
            raise HTTPException(status_code=504, detail="Waline服务响应超时")
        except httpx.HTTPStatusError as e:
            logger.error(f"Waline API请求失败: {e.response.status_code} - {e.response.text}")
            try:
                error_data = e.response.json()
                raise HTTPException(
                    status_code=e.response.status_code,
                    detail=error_data.get("errmsg", f"Waline API错误: {e.response.text}")
                )
            except:
                raise HTTPException(
                    status_code=e.response.status_code,
                    detail=f"Waline API错误: {e.response.text}"
                )
        except Exception as e:
            logger.error(f"Waline API请求异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

# === 评论相关API（符合官方规范） ===

@router.get("/comment", summary="获取评论列表")
async def get_comments(
    path: str = Query(..., description="页面路径"),
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(10, ge=1, le=100, description="每页数量"),
    sortBy: str = Query("createdAt", description="排序字段"),
    token: Optional[str] = Query(None, description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定页面的评论列表
    符合官方getComment API规范
    """
    params = {
        "path": path,
        "page": page,
        "pageSize": pageSize,
        "sortBy": sortBy
    }

    result = await make_waline_request(db, "comment", params=params, token=token)
    return result

@router.post("/comment", summary="添加评论")
async def add_comment(
    comment_data: WalineCommentData,
    token: Optional[str] = Query(None, description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    添加新评论
    符合官方addComment API规范
    """
    result = await make_waline_request(
        db,
        "comment",
        method="POST",
        data=comment_data.dict(exclude_none=True),
        token=token
    )
    return result

@router.put("/comment/{object_id}", summary="更新评论")
async def update_comment(
    object_id: int,
    comment_data: UpdateCommentData,
    token: str = Query(..., description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    更新评论
    符合官方updateComment API规范
    """
    result = await make_waline_request(
        db,
        f"comment/{object_id}",
        method="PUT",
        data=comment_data.dict(exclude_none=True),
        token=token
    )
    return result

@router.delete("/comment/{object_id}", summary="删除评论")
async def delete_comment(
    object_id: int,
    token: str = Query(..., description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    删除评论
    符合官方deleteComment API规范
    """
    result = await make_waline_request(db, f"comment/{object_id}", method="DELETE", token=token)
    return result

@router.get("/comment/recent", summary="获取最近评论")
async def get_recent_comments(
    count: int = Query(10, ge=1, le=50, description="获取数量"),
    token: Optional[str] = Query(None, description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取最近评论
    符合官方getRecentComment API规范
    """
    params = {"count": count}

    # 使用官方的recent评论端点
    result = await make_waline_request(db, "comment/recent", params=params, token=token)
    return result

@router.get("/comment/count", summary="获取评论计数")
async def get_comment_count(
    paths: List[str] = Query(..., description="页面路径列表"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取多个页面的评论计数
    符合官方fetchCommentCount API规范
    """
    # 将路径列表转换为查询参数
    params = {}
    for i, path in enumerate(paths):
        params[f"paths[{i}]"] = path

    result = await make_waline_request(db, "comment/count", params=params)
    return result

# === 页面浏览量API ===

@router.get("/pageview", summary="获取页面浏览量")
async def get_pageview(
    paths: List[str] = Query(..., description="页面路径列表"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取页面浏览量
    符合官方getPageview API规范
    """
    # 将路径列表转换为查询参数
    params = {}
    for i, path in enumerate(paths):
        params[f"paths[{i}]"] = path

    result = await make_waline_request(db, "pageview", params=params)
    return result

@router.post("/pageview", summary="更新页面浏览量")
async def update_pageview(
    path_data: dict = Body(..., description="包含path字段的数据"),
    db: AsyncSession = Depends(get_db)
):
    """
    更新页面浏览量
    符合官方updatePageview API规范
    """
    result = await make_waline_request(db, "pageview", method="POST", data=path_data)
    return result

# === 文章计数器API ===

@router.get("/counter", summary="获取文章计数器")
async def get_article_counter(
    paths: List[str] = Query(..., description="页面路径列表"),
    type: List[str] = Query(..., description="计数器类型列表"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取文章计数器
    符合官方getArticleCounter API规范
    """
    # 将列表转换为查询参数
    params = {}
    for i, path in enumerate(paths):
        params[f"paths[{i}]"] = path
    for i, counter_type in enumerate(type):
        params[f"type[{i}]"] = counter_type

    result = await make_waline_request(db, "counter", params=params)
    return result

@router.post("/counter", summary="更新文章计数器")
async def update_article_counter(
    counter_data: dict = Body(..., description="计数器更新数据"),
    db: AsyncSession = Depends(get_db)
):
    """
    更新文章计数器
    符合官方updateArticleCounter API规范
    """
    result = await make_waline_request(db, "counter", method="POST", data=counter_data)
    return result

# === 用户管理API ===

@router.get("/user", summary="获取用户列表")
async def get_user_list(
    pageSize: int = Query(10, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户列表
    符合官方getUserList API规范
    """
    params = {"pageSize": pageSize}

    result = await make_waline_request(db, "user", params=params)
    return result

# === 认证API ===

@router.post("/login", summary="管理员登录")
async def login(
    login_data: Optional[dict] = Body(None, description="登录数据"),
    db: AsyncSession = Depends(get_db)
):
    """
    管理员登录
    符合官方login API规范
    """
    server_url, lang = await get_waline_config(db)

    # 构建基础API选项
    base_options = {
        "serverURL": server_url,
        "lang": lang
    }

    # 如果有额外的登录数据，合并进去
    if login_data:
        base_options.update(login_data)

    try:
        result = await make_waline_request(db, "token", method="POST", data=base_options)

        # 确保返回格式一致
        if "data" not in result:
            result = {"errno": 0, "errmsg": "success", "data": result}

        return result
    except Exception as e:
        logger.error(f"Waline登录失败: {str(e)}")
        return {"errno": 1, "errmsg": f"登录失败: {str(e)}", "data": None}

@router.post("/logout", summary="管理员登出")
async def logout(
    token: str = Query(..., description="用户令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    管理员登出
    清除服务端session
    """
    try:
        # 尝试调用Waline的登出接口（如果存在）
        result = await make_waline_request(db, "logout", method="POST", token=token)
        return result
    except Exception as e:
        # 即使服务端登出失败，也返回成功，让客户端清除本地状态
        logger.warning(f"Waline登出失败: {str(e)}")
        return {"errno": 0, "errmsg": "success", "data": ""}

@router.post("/token/refresh", summary="刷新Token")
async def refresh_token(
    token: str = Query(..., description="当前令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    刷新用户Token
    """
    try:
        # 尝试使用当前token获取新token
        result = await make_waline_request(db, "token/refresh", method="POST", token=token)
        return result
    except Exception as e:
        logger.error(f"刷新Token失败: {str(e)}")
        return {"errno": 1, "errmsg": f"刷新Token失败: {str(e)}", "data": None}

@router.post("/token/validate", summary="验证Token")
async def validate_token(
    token: str = Query(..., description="要验证的令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    验证Token有效性
    """
    try:
        # 尝试使用token调用一个简单的API来验证
        await make_waline_request(db, "user", params={"pageSize": 1}, token=token, timeout=5)
        return {"errno": 0, "errmsg": "success", "data": {"valid": True}}
    except Exception as e:
        logger.warning(f"Token验证失败: {str(e)}")
        return {"errno": 0, "errmsg": "success", "data": {"valid": False}}

# === 管理员API ===

@router.get("/admin/comment", summary="管理员获取评论")
async def admin_get_comments(
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(10, ge=1, le=100, description="每页数量"),
    type: Optional[str] = Query(None, description="评论类型筛选"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    token: str = Query(..., description="管理员令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    管理员获取评论列表
    支持筛选和搜索功能
    """
    params = {
        "page": page,
        "pageSize": pageSize
    }

    if type:
        params["type"] = type
    if keyword:
        params["keyword"] = keyword

    result = await make_waline_request(db, "comment", params=params, token=token)
    return result

@router.put("/admin/comment/{object_id}/status", summary="管理员更新评论状态")
async def admin_update_comment_status(
    object_id: int,
    status_data: dict = Body(..., description="状态更新数据"),
    token: str = Query(..., description="管理员令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    管理员更新评论状态
    支持批量状态更新
    """
    result = await make_waline_request(
        db,
        f"comment/{object_id}",
        method="PUT",
        data=status_data,
        token=token
    )
    return result

# === 批量操作API ===

@router.post("/admin/comment/batch", summary="批量操作评论")
async def batch_comment_operation(
    operation_data: dict = Body(..., description="批量操作数据"),
    token: str = Query(..., description="管理员令牌"),
    db: AsyncSession = Depends(get_db)
):
    """
    批量操作评论
    支持批量审核、删除等操作
    """
    action = operation_data.get("action")
    comment_ids = operation_data.get("comment_ids", [])

    if not action or not comment_ids:
        raise HTTPException(status_code=400, detail="缺少必要的操作参数")

    results = []
    errors = []

    for comment_id in comment_ids:
        try:
            if action == "delete":
                result = await make_waline_request(
                    db,
                    f"comment/{comment_id}",
                    method="DELETE",
                    token=token
                )
            else:
                # 状态更新操作
                result = await make_waline_request(
                    db,
                    f"comment/{comment_id}",
                    method="PUT",
                    data={"status": action},
                    token=token
                )
            results.append({"comment_id": comment_id, "success": True, "result": result})
        except Exception as e:
            errors.append({"comment_id": comment_id, "error": str(e)})

    return {
        "success_count": len(results),
        "error_count": len(errors),
        "results": results,
        "errors": errors
    }

# === 健康检查 ===

@router.get("/health", summary="Waline服务健康检查")
async def waline_health_check(db: AsyncSession = Depends(get_db)):
    """
    检查Waline服务状态
    包括基础连接和API功能测试
    """
    try:
        server_url, lang = await get_waline_config(db)

        # 基础连接测试
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get(f"{server_url}/api")
            response.raise_for_status()

        # API功能测试
        test_results = {}

        try:
            # 测试用户API
            await make_waline_request(db, "user", params={"pageSize": 1}, timeout=5)
            test_results["user_api"] = "正常"
        except Exception as e:
            test_results["user_api"] = f"异常: {str(e)}"

        try:
            # 测试评论计数API
            await make_waline_request(db, "comment/count", params={"paths[0]": "/"}, timeout=5)
            test_results["comment_count_api"] = "正常"
        except Exception as e:
            test_results["comment_count_api"] = f"异常: {str(e)}"

        return {
            "status": "healthy",
            "server_url": server_url,
            "lang": lang,
            "timestamp": datetime.now().isoformat(),
            "api_tests": test_results,
            "message": "Waline服务正常"
        }
    except Exception as e:
        logger.error(f"Waline健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "server_url": server_url if 'server_url' in locals() else "unknown",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "message": f"Waline服务异常: {str(e)}"
        }