from fastapi import APIRouter, HTTPException, Query, Depends, Body
from sqlalchemy.ext.asyncio import AsyncSession
from database.database import get_db
from typing import Optional, List, Dict, Any
import httpx
from datetime import datetime, timedelta
from pydantic import BaseModel
from routers.system_config import get_config_value
from utils.auth import get_current_user
import logging
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/waline-management", tags=["waline-management"])

DEFAULT_TIMEOUT = 30

class WalineStats(BaseModel):
    total_comments: int = 0
    approved_comments: int = 0
    pending_comments: int = 0
    spam_comments: int = 0
    deleted_comments: int = 0
    total_users: int = 0
    total_pageviews: int = 0
    cache_hit_rate: float = 0.0

async def get_waline_config(db: AsyncSession) -> tuple[str, str]:
    """获取Waline配置"""
    server_url = await get_config_value(db, "waline.server_url", "https://waline.jyaochen.cn")
    lang = await get_config_value(db, "waline.lang", "zh-CN")
    return server_url, lang

async def make_waline_request_with_retry(
    db: AsyncSession,
    endpoint: str, 
    params: Optional[Dict] = None, 
    method: str = "GET",
    data: Optional[Dict] = None,
    token: Optional[str] = None,
    retries: int = 3
) -> Dict[str, Any]:
    """
    带重试机制的Waline请求
    """
    server_url, lang = await get_waline_config(db)
    url = f"{server_url}/api/{endpoint.lstrip('/')}"
    
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    for attempt in range(retries):
        try:
            async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
                if method.upper() == "GET":
                    response = await client.get(url, params=params or {}, headers=headers)
                elif method.upper() == "POST":
                    response = await client.post(url, params=params or {}, json=data or {}, headers=headers)
                elif method.upper() == "PUT":
                    response = await client.put(url, params=params or {}, json=data or {}, headers=headers)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, params=params or {}, headers=headers)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                response.raise_for_status()
                return response.json()
                
        except (httpx.TimeoutException, httpx.HTTPStatusError) as e:
            if attempt == retries - 1:  # 最后一次重试
                logger.error(f"Waline API请求失败 (重试{retries}次): {url} - {str(e)}")
                raise HTTPException(status_code=504, detail="Waline服务不可用")
            else:
                logger.warning(f"Waline API请求失败，正在重试 ({attempt + 1}/{retries}): {url}")
                await asyncio.sleep(1)  # 等待1秒后重试
        except Exception as e:
            logger.error(f"Waline API请求异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

# === 综合统计API ===

@router.get("/stats", response_model=WalineStats, summary="获取Waline综合统计")
async def get_waline_stats(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取Waline系统综合统计数据"""
    try:
        # 并发获取多个数据源
        tasks = [
            make_waline_request_with_retry(db, "comment", {"type": "recent", "count": 100}),
            make_waline_request_with_retry(db, "user", {"pageSize": 100}),
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        stats = WalineStats()
        
        # 处理评论数据
        if not isinstance(results[0], Exception) and results[0].get("data"):
            comments = results[0]["data"]
            stats.total_comments = len(comments)
            stats.approved_comments = len([c for c in comments if c.get("status") == "approved"])
            stats.pending_comments = len([c for c in comments if c.get("status") == "waiting"])
            stats.spam_comments = len([c for c in comments if c.get("status") == "spam"])
        
        # 处理用户数据
        if not isinstance(results[1], Exception) and results[1].get("data"):
            users = results[1]["data"]
            stats.total_users = len(users)
        
        return stats
        
    except Exception as e:
        logger.error(f"获取Waline统计失败: {e}")
        return WalineStats()  # 返回空统计而不是错误

@router.get("/comments/all", summary="获取所有评论（管理员）")
async def get_all_comments(
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """管理员获取所有评论，支持筛选和搜索"""
    try:
        # 获取最近评论（数量更大以支持筛选）
        params = {"type": "recent", "count": 200}
        result = await make_waline_request_with_retry(db, "comment", params)
        
        comments = result.get("data", [])
        
        # 服务端筛选
        if status and status != "all":
            comments = [c for c in comments if c.get("status") == status]
        
        if keyword:
            keyword_lower = keyword.lower()
            comments = [
                c for c in comments 
                if (keyword_lower in c.get("comment", "").lower() or 
                    keyword_lower in c.get("nick", "").lower())
            ]
        
        # 分页
        total = len(comments)
        start_idx = (page - 1) * pageSize
        end_idx = start_idx + pageSize
        paginated_comments = comments[start_idx:end_idx]
        
        return {
            "data": paginated_comments,
            "total": total,
            "page": page,
            "pageSize": pageSize,
            "totalPages": (total + pageSize - 1) // pageSize
        }
        
    except Exception as e:
        logger.error(f"获取评论列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取评论列表失败")

@router.post("/comments/batch-action", summary="批量操作评论")
async def batch_comment_action(
    action: str = Body(..., description="操作类型: approved, waiting, spam, delete"),
    comment_ids: List[int] = Body(..., description="评论ID列表"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """批量操作评论（需要管理员权限）"""
    if not comment_ids:
        raise HTTPException(status_code=400, detail="请选择要操作的评论")
    
    if action not in ["approved", "waiting", "spam", "delete"]:
        raise HTTPException(status_code=400, detail="无效的操作类型")
    
    try:
        success_count = 0
        error_count = 0
        errors = []
        
        for comment_id in comment_ids:
            try:
                if action == "delete":
                    await make_waline_request_with_retry(
                        db, f"comment/{comment_id}", 
                        method="DELETE", 
                        token="admin-token"  # 这里需要从认证系统获取实际token
                    )
                else:
                    await make_waline_request_with_retry(
                        db, f"comment/{comment_id}", 
                        method="PUT", 
                        data={"status": action},
                        token="admin-token"
                    )
                success_count += 1
            except Exception as e:
                error_count += 1
                errors.append(f"评论 {comment_id}: {str(e)}")
        
        return {
            "success_count": success_count,
            "error_count": error_count,
            "errors": errors,
            "message": f"操作完成，成功 {success_count} 条，失败 {error_count} 条"
        }
        
    except Exception as e:
        logger.error(f"批量操作评论失败: {e}")
        raise HTTPException(status_code=500, detail="批量操作失败")

@router.get("/users/all", summary="获取所有用户")
async def get_all_users(
    pageSize: int = Query(50, ge=1, le=200, description="每页数量"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取所有用户列表"""
    try:
        params = {"pageSize": pageSize}
        result = await make_waline_request_with_retry(db, "user", params)
        return result
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户列表失败")

@router.get("/pageviews/summary", summary="获取页面浏览量汇总")
async def get_pageviews_summary(
    paths: List[str] = Query([], description="页面路径列表"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取页面浏览量汇总数据"""
    try:
        if not paths:
            # 如果没有指定路径，获取常见路径的浏览量
            paths = ["/", "/blogs", "/projects", "/gallery", "/about"]
        
        params = {"paths": paths}
        result = await make_waline_request_with_retry(db, "article", params)
        return result
    except Exception as e:
        logger.error(f"获取页面浏览量失败: {e}")
        raise HTTPException(status_code=500, detail="获取页面浏览量失败")

@router.post("/system/health-check", summary="系统健康检查")
async def system_health_check(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """完整的系统健康检查"""
    try:
        server_url, lang = await get_waline_config(db)
        
        # 检查基本连接
        async with httpx.AsyncClient(timeout=10) as client:
            response = await client.get(f"{server_url}/api")
            response.raise_for_status()
        
        # 检查API功能
        test_results = {}
        
        # 测试评论API
        try:
            await make_waline_request_with_retry(db, "comment", {"type": "recent", "count": 1})
            test_results["comment_api"] = "正常"
        except Exception as e:
            test_results["comment_api"] = f"异常: {str(e)}"
        
        # 测试用户API
        try:
            await make_waline_request_with_retry(db, "user", {"pageSize": 1})
            test_results["user_api"] = "正常"
        except Exception as e:
            test_results["user_api"] = f"异常: {str(e)}"
        
        return {
            "status": "healthy",
            "server_url": server_url,
            "lang": lang,
            "timestamp": datetime.now().isoformat(),
            "api_tests": test_results,
            "message": "系统运行正常"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "server_url": server_url if 'server_url' in locals() else "unknown",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "message": "系统运行异常"
        }

@router.post("/system/cache-clear", summary="清除缓存")
async def clear_system_cache(
    cache_type: str = Body("all", description="缓存类型: all, comments, users, pageviews"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """清除系统缓存"""
    try:
        # 这里可以实现实际的缓存清除逻辑
        # 目前返回成功状态
        return {
            "status": "success",
            "cache_type": cache_type,
            "timestamp": datetime.now().isoformat(),
            "message": f"缓存清除成功: {cache_type}"
        }
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail="清除缓存失败")

@router.get("/export/comments", summary="导出评论数据")
async def export_comments(
    format: str = Query("json", description="导出格式: json, csv"),
    status: Optional[str] = Query(None, description="状态筛选"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """导出评论数据（管理员功能）"""
    try:
        # 获取评论数据
        params = {"type": "recent", "count": 500}  # 获取更多数据用于导出
        result = await make_waline_request_with_retry(db, "comment", params)
        
        comments = result.get("data", [])
        
        # 应用筛选条件
        if status and status != "all":
            comments = [c for c in comments if c.get("status") == status]
        
        if start_date:
            start_timestamp = datetime.fromisoformat(start_date).timestamp() * 1000
            comments = [c for c in comments if c.get("time", 0) >= start_timestamp]
        
        if end_date:
            end_timestamp = datetime.fromisoformat(end_date).timestamp() * 1000
            comments = [c for c in comments if c.get("time", 0) <= end_timestamp]
        
        # 根据格式返回数据
        if format == "csv":
            # 这里可以实现CSV格式转换
            return {
                "format": "csv",
                "data": comments,
                "count": len(comments),
                "message": "CSV导出功能待实现"
            }
        else:
            return {
                "format": "json",
                "data": comments,
                "count": len(comments),
                "exported_at": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"导出评论数据失败: {e}")
        raise HTTPException(status_code=500, detail="导出数据失败")