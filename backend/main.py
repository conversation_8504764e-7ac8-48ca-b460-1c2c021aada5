from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from database.database import get_db, engine, Base
from app.config import settings
from middleware.api_metrics import APIMetricsMiddleware
from tasks.system_monitor_task import system_monitor_task
import os
import logging
import asyncio
from pathlib import Path

# 导入路由
from routers import (
    blogs, images, auth, users,
    stats, site_settings, layout_manager, theme_manager,
    content_manager, tags, seo, image_categories, gallery, about,
    template_categories, icons, favorites, personal_info, website_versions,
    slug, system_monitor, waline_api, system_config, waline_management
)

# 配置日志
def setup_logging():
    """配置日志级别"""
    if settings.ENVIRONMENT == "development" or settings.DEBUG:
        # 开发环境：显示所有日志
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        # 生产环境：只显示WARNING及以上级别的日志
        logging.basicConfig(
            level=logging.WARNING,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        # 禁用uvicorn的INFO日志
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
        logging.getLogger("uvicorn").setLevel(logging.WARNING)
        logging.getLogger("fastapi").setLevel(logging.WARNING)

# 初始化日志
setup_logging()

# 创建上传目录
upload_dir = Path(settings.UPLOAD_DIR)
if not upload_dir.exists():
    upload_dir.mkdir(parents=True, exist_ok=True)

# 创建应用
app = FastAPI(
    title=settings.APP_NAME,
    description="Backend API for personal portfolio website",
    version="1.0.0",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加API性能监控中间件
app.add_middleware(APIMetricsMiddleware)

# 挂载静态文件
app.mount("/uploads", StaticFiles(directory=settings.UPLOAD_DIR), name="uploads")

# 注册路由
app.include_router(auth.router, prefix="/api")
app.include_router(users.router, prefix="/api")
app.include_router(site_settings.router, prefix="/api")
app.include_router(blogs.router, prefix="/api")
app.include_router(images.router, prefix="/api")
app.include_router(image_categories.router, prefix="/api")

app.include_router(stats.router, prefix="/api")
app.include_router(layout_manager.router, prefix="/api")
app.include_router(theme_manager.router, prefix="/api")
app.include_router(content_manager.router, prefix="/api")
app.include_router(tags.router, prefix="/api")
app.include_router(seo.router, prefix="/api")
app.include_router(gallery.router, prefix="/api")
app.include_router(about.router, prefix="/api")
app.include_router(template_categories.router, prefix="/api")
app.include_router(icons.router, prefix="/api")
app.include_router(favorites.router)
app.include_router(personal_info.router, prefix="/api")
app.include_router(personal_info.public_router, prefix="/api")
app.include_router(website_versions.router, prefix="/api")
app.include_router(slug.router, prefix="/api/slug", tags=["slug"])
app.include_router(system_monitor.router, prefix="/api")
app.include_router(system_monitor.public_router, prefix="/api")
app.include_router(waline_api.router, prefix="/api")
app.include_router(system_config.router, prefix="/api")
app.include_router(waline_management.router, prefix="/api")

# 导入所有模型以确保表创建
from models.system_config import SystemConfig

# 创建数据库表
@app.on_event("startup")
async def init_db():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)



# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "Service is running"}

# 根路由
@app.get("/")
async def root():
    return {
        "message": "Welcome to Portfolio Backend API",
        "docs": "/docs",
        "redoc": "/redoc"
    }

# 启动和关闭事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    # 启动系统监控任务
    asyncio.create_task(system_monitor_task.start())

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    # 停止系统监控任务
    system_monitor_task.stop()

# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": str(exc)},
    )
